[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://localhost:27017/test"

[golog]
dir = "./test_logs"
level = "warn"
format = "text"

[write_options]
max_retries = 2
retry_delay = "10ms"
s3_timeout = "5s"
chunk_size = 1048576
enable_logging = false
validate_content = true
enable_metadata = true

[connection_sources]

[[connection_sources.s3_providers]]
name = "test-garage-primary"
endpoint = "http://127.0.0.1:3900"
key = "GK4d1095e87554826d40f6af89"
pass = "02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
region = "garage"

[[connection_sources.s3_providers]]
name = "test-garage-secondary"
endpoint = "http://127.0.0.1:3900"
key = "GK1e8266fe625fd9e3d392d497"
pass = "0130431df8aa44ddee23777cfdc369b8dab1f51777d4308b49655aa0bdd85bc1"
region = "garage"

[userupload]
site = "TEST"

[[userupload.types]]
entryName = "test_file"
prefix = "/test/files"
tmpPath = "/tmp/goupload_test/files"
maxSize = "100MB"
storage = [
  { type = "local", path = "test_uploads/files" }
]

[[userupload.types]]
entryName = "test_image"
prefix = "/test/images"
tmpPath = "/tmp/goupload_test/images"
maxSize = "50MB"
storage = [
  { type = "local", path = "test_uploads/images" }
]

[[userupload.types]]
entryName = "test_video"
prefix = "/test/videos"
tmpPath = "/tmp/goupload_test/videos"
maxSize = "500MB"
storage = [
  { type = "local", path = "test_uploads/videos" }
]

[[userupload.types]]
entryName = "test_s3_only"
prefix = "/test/s3"
tmpPath = "/tmp/goupload_test/s3"
maxSize = "200MB"
storage = [
  { type = "s3", target = "test-garage-primary", bucket = "test-bucket" }
]

[[userupload.types]]
entryName = "test_multi_storage"
prefix = "/test/multi"
tmpPath = "/tmp/goupload_test/multi"
maxSize = "300MB"
storage = [
  { type = "local", path = "test_uploads/multi" },
  { type = "s3", target = "test-garage-primary", bucket = "test-multi-bucket" }
]

[[userupload.types]]
entryName = "test_small_limit"
prefix = "/test/small"
tmpPath = "/tmp/goupload_test/small"
maxSize = "1KB"
storage = [
  { type = "local", path = "test_uploads/small" }
]

[[userupload.types]]
entryName = "documents"
prefix = "/test/documents"
tmpPath = "/tmp/goupload_test/documents"
maxSize = "100MB"
storage = [
  { type = "local", path = "test_uploads/documents" }
]
